class_name <PERSON><PERSON>
extends Node2D

signal start_run

@export var portal: Portal
@export var meta_currency_label: Label

func _ready() -> void:
	portal.player_entered.connect(_on_portal_player_entered)
	_update_meta_currency_display()

func _on_portal_player_entered() -> void:
	start_run.emit()

func _update_meta_currency_display() -> void:
	var currency: int = GameProgress.get_meta_currency()
	meta_currency_label.text = "Мета-валюта: " + str(currency)
