extends Node

const SAVE_PATH = "user://progress.dat"

var _completed_levels: Dictionary = {}
var _meta_currency: int = 0

func _ready() -> void:
	load_progress()

func is_level_completed(level_id: String) -> bool:
	return _completed_levels.has(level_id)

func mark_level_as_completed(level_id: String) -> void:
	if not level_id.is_empty():
		_completed_levels[level_id] = true
		save_progress()

func add_meta_currency(amount: int) -> void:
	if amount > 0:
		_meta_currency += amount
		save_progress()

func get_meta_currency() -> int:
	return _meta_currency

func save_progress() -> void:
	var file: FileAccess = FileAccess.open(SAVE_PATH, FileAccess.WRITE)
	if file:
		var save_data: Dictionary = {
			"completed_levels": _completed_levels,
			"meta_currency": _meta_currency
		}
		var json_string: String = JSON.stringify(save_data)
		file.store_string(json_string)

func load_progress() -> void:
	if not FileAccess.file_exists(SAVE_PATH):
		return

	var file: FileAccess = FileAccess.open(SAVE_PATH, FileAccess.READ)
	if file:
		var json_string: String = file.get_as_text()
		var parse_result: Variant = JSON.parse_string(json_string)
		if parse_result is Dictionary:
			var save_data: Dictionary = parse_result
			if save_data.has("completed_levels") and save_data["completed_levels"] is Dictionary:
				_completed_levels = save_data["completed_levels"]
			elif parse_result is Dictionary:
				_completed_levels = parse_result

			if save_data.has("meta_currency") and save_data["meta_currency"] is int:
				_meta_currency = save_data["meta_currency"]
